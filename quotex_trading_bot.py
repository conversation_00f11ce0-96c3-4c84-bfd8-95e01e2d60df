#!/usr/bin/env python3
"""
Advanced Trading Bot with Quotex Integration
Features: Live trading, signal generation, and automated execution

QuotexPy Setup Instructions:
1. Install quotexpy: pip install quotexpy
2. Install python-dotenv: pip install python-dotenv
3. Create .env file with:
   QUOTEX_EMAIL=<EMAIL>
   QUOTEX_PASSWORD=Uz2309##2309
4. Keep .env file secure and never commit to version control
"""

import os
import sys
import time
import asyncio
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Quotex Trading Pairs - Verified pairs supported by QuotexPy
QUOTEX_LIVE_PAIRS = [
    # Major Forex Pairs (Live Market Hours)
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURJPY",
    "GBPJPY", "EURGBP", "AUDJPY", "EURAUD", "GBPAUD", "NZDCAD", "CADCHF", "CHFJPY",
    "EURCHF", "GBPCHF", "AUDCHF", "NZDCHF", "EURCZK", "USDCZK", "USDHUF", "USDPLN",

    # Crypto Pairs
    "BTCUSD", "ETHUSD", "LTCUSD", "XRPUSD", "ADAUSD", "DOTUSD", "LINKUSD", "BCHUSD",
    "EOSUSD", "XLMUSD", "TRXUSD", "BNBUSD", "SOLUSD", "AVAXUSD", "MATICUSD", "DOGEUSD",

    # Commodities
    "XAUUSD", "XAGUSD", "WTIUSD", "BRENTUSD", "NATGAS", "COPPER", "PLATINUM", "PALLADIUM",

    # Indices
    "SPX500", "NAS100", "US30", "GER30", "UK100", "FRA40", "JPN225", "AUS200"
]

QUOTEX_OTC_PAIRS = [
    # OTC Forex Pairs (Available 24/7)
    "EURUSD_OTC", "GBPUSD_OTC", "USDJPY_OTC", "AUDUSD_OTC", "USDCAD_OTC", "USDCHF_OTC",
    "NZDUSD_OTC", "EURJPY_OTC", "GBPJPY_OTC", "EURGBP_OTC", "AUDJPY_OTC", "EURAUD_OTC",
    "EURCHF_OTC", "GBPCHF_OTC", "AUDCHF_OTC", "NZDCHF_OTC", "GBPCAD_OTC", "AUDCAD_OTC",

    # OTC Commodities
    "XAUUSD_OTC", "XAGUSD_OTC", "WTIUSD_OTC", "BRENTUSD_OTC",

    # OTC Indices
    "SPX500_OTC", "NAS100_OTC", "GER30_OTC", "UK100_OTC", "FRA40_OTC", "JPN225_OTC"
]

# Combined pairs list
ALL_QUOTEX_PAIRS = QUOTEX_LIVE_PAIRS + QUOTEX_OTC_PAIRS

# Available timeframes
TIMEFRAMES = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]

# Available strategies
STRATEGIES = [
    "momentum_breakout", "support_resistance", "trend_following", "reversal_pattern",
    "volume_spike", "fibonacci_retracement", "bollinger_bands", "macd_divergence",
    "rsi_oversold", "moving_average_cross", "price_action", "harmonic_pattern"
]

def print_colored(text, color="white"):
    """Print colored text to console"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")

def display_pairs_in_columns(pairs, columns=4, title="📊 Available Trading Pairs:", start_index=0):
    """Display trading pairs in specified number of columns"""
    if title:
        print_colored(f"\n{title}", "cyan")
        print_colored("=" * 80, "blue")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<15}"
        print_colored(formatted_row, "green")

    if title:
        print_colored("=" * 80, "blue")

def select_pair():
    """Allow user to select a trading pair with separate Live and OTC sections"""
    while True:
        print_colored("\n📊 Available Trading Pairs:", "cyan")
        print_colored("=" * 80, "blue")

        print_colored("\n🌍 LIVE PAIRS (Market Hours Only):", "yellow")
        display_pairs_in_columns(QUOTEX_LIVE_PAIRS, title="")

        print_colored("\n🔄 OTC PAIRS (Available 24/7):", "yellow")
        display_pairs_in_columns(QUOTEX_OTC_PAIRS, title="", start_index=len(QUOTEX_LIVE_PAIRS))

        try:
            choice = input(f"\nSelect pair (1-{len(ALL_QUOTEX_PAIRS)}) or type pair name: ").strip()

            # Check if user entered a number
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(ALL_QUOTEX_PAIRS):
                    selected_pair = ALL_QUOTEX_PAIRS[index]
                    print_colored(f"✅ Selected pair: {selected_pair}", "green")
                    return selected_pair
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")

            # Check if user typed pair name
            elif choice.upper() in ALL_QUOTEX_PAIRS:
                selected_pair = choice.upper()
                print_colored(f"✅ Selected pair: {selected_pair}", "green")
                return selected_pair

            else:
                print_colored("❌ Invalid pair. Please try again.", "red")

        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_timeframe():
    """Allow user to select a timeframe"""
    while True:
        print_colored("\n⏰ Available Timeframes:", "cyan")
        print_colored("=" * 40, "blue")
        
        for i, tf in enumerate(TIMEFRAMES, 1):
            print_colored(f"{i}. {tf}", "green")
        
        print_colored("=" * 40, "blue")
        
        try:
            choice = input(f"Select timeframe (1-{len(TIMEFRAMES)}): ").strip()
            
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(TIMEFRAMES):
                    selected_tf = TIMEFRAMES[index]
                    print_colored(f"✅ Selected timeframe: {selected_tf}", "green")
                    return selected_tf
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            else:
                print_colored("❌ Invalid input. Please enter a number.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_strategy():
    """Allow user to select a trading strategy"""
    while True:
        print_colored("\n🎯 Available Trading Strategies:", "cyan")
        print_colored("=" * 60, "blue")
        
        # Display strategies in 2 columns
        for i in range(0, len(STRATEGIES), 2):
            row = STRATEGIES[i:i+2]
            formatted_row = ""
            for j, strategy in enumerate(row):
                formatted_row += f"{i+j+1:2d}. {strategy:<25}"
            print_colored(formatted_row, "green")
        
        print_colored("=" * 60, "blue")
        
        try:
            choice = input(f"Select strategy (1-{len(STRATEGIES)}): ").strip()
            
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(STRATEGIES):
                    selected_strategy = STRATEGIES[index]
                    print_colored(f"✅ Selected strategy: {selected_strategy}", "green")
                    return selected_strategy
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            else:
                print_colored("❌ Invalid input. Please enter a number.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_account_type():
    """Allow user to select account type"""
    account_types = ["practice", "quotex_demo", "quotex_live"]
    
    while True:
        print_colored("\n💼 Account Type Selection:", "cyan")
        print_colored("=" * 50, "blue")
        print_colored("1. Practice (Signal display only)", "green")
        print_colored("2. Quotex Demo (Demo trading)", "yellow")
        print_colored("3. Quotex Live (Live trading)", "red")
        print_colored("=" * 50, "blue")
        
        try:
            choice = input("Select account type (1-3): ").strip()
            
            if choice in ['1', '2', '3']:
                selected_type = account_types[int(choice) - 1]
                
                if selected_type == "practice":
                    print_colored("✅ Practice mode selected - signals only", "green")
                elif selected_type == "quotex_demo":
                    print_colored("⚠️  Demo trading mode selected", "yellow")
                elif selected_type == "quotex_live":
                    print_colored("🚨 LIVE trading mode selected - REAL MONEY!", "red")
                    confirm = input("Type 'CONFIRM' to proceed with live trading: ").strip()
                    if confirm != 'CONFIRM':
                        print_colored("❌ Live trading cancelled", "yellow")
                        continue
                
                return selected_type
            else:
                print_colored("❌ Invalid choice. Please select 1, 2, or 3.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_trade_amount():
    """Allow user to select trade amount"""
    while True:
        print_colored("\n💰 Trade Amount Selection:", "cyan")
        print_colored("=" * 40, "blue")
        print_colored("1. $1", "green")
        print_colored("2. $2", "green")
        print_colored("3. $5", "green")
        print_colored("4. $10", "green")
        print_colored("5. $20", "green")
        print_colored("6. $50", "green")
        print_colored("7. $100", "green")
        print_colored("8. Custom amount", "yellow")
        print_colored("=" * 40, "blue")

        try:
            choice = input("Select trade amount (1-8): ").strip()

            amounts = [1, 2, 5, 10, 20, 50, 100]

            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                amount = amounts[int(choice) - 1]
                print_colored(f"✅ Selected amount: ${amount}", "green")
                return amount
            elif choice == '8':
                while True:
                    try:
                        custom_amount = float(input("Enter custom amount ($): ").strip())
                        if custom_amount > 0:
                            print_colored(f"✅ Selected amount: ${custom_amount}", "green")
                            return custom_amount
                        else:
                            print_colored("❌ Amount must be greater than 0", "red")
                    except ValueError:
                        print_colored("❌ Invalid amount. Please enter a number.", "red")
            else:
                print_colored("❌ Invalid choice. Please select 1-8.", "red")

        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def get_signal(pair, timeframe, strategy):
    """
    Simulate signal generation based on pair, timeframe, and strategy
    In a real implementation, this would contain actual technical analysis
    """
    import random
    
    # Simulate different signal probabilities based on strategy
    signal_weights = {
        "momentum_breakout": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "support_resistance": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "trend_following": {"call": 0.40, "put": 0.25, "no signal": 0.35},
        "reversal_pattern": {"call": 0.25, "put": 0.40, "no signal": 0.35},
        "volume_spike": {"call": 0.45, "put": 0.20, "no signal": 0.35},
        "fibonacci_retracement": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "bollinger_bands": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "macd_divergence": {"call": 0.40, "put": 0.30, "no signal": 0.30},
        "rsi_oversold": {"call": 0.50, "put": 0.15, "no signal": 0.35},
        "moving_average_cross": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "price_action": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "harmonic_pattern": {"call": 0.25, "put": 0.25, "no signal": 0.50}
    }
    
    weights = signal_weights.get(strategy, {"call": 0.33, "put": 0.33, "no signal": 0.34})
    signals = list(weights.keys())
    probabilities = list(weights.values())
    
    signal = random.choices(signals, weights=probabilities)[0]
    
    # Add some randomness for confidence
    if signal != "no signal":
        confidence = random.uniform(0.65, 0.95)
        return signal, confidence
    else:
        return signal, 0.0

def log_action(message, level="INFO"):
    """Log actions with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    color_map = {
        "INFO": "cyan",
        "SUCCESS": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "SIGNAL": "purple"
    }
    color = color_map.get(level, "white")
    print_colored(f"[{timestamp}] {level}: {message}", color)

async def connect_to_quotex(account_type):
    """
    Connect to Quotex using quotexpy
    Returns connection object or None if practice mode
    """
    if account_type == "practice":
        log_action("Practice mode - no Quotex connection needed", "INFO")
        return None

    try:
        # Import quotexpy (will fail if not installed)
        from quotexpy import Quotex

        # Get credentials from environment
        email = os.getenv("QUOTEX_EMAIL")
        password = os.getenv("QUOTEX_PASSWORD")

        if not email or not password:
            log_action("Quotex credentials not found in .env file", "ERROR")
            log_action("Please set QUOTEX_EMAIL and QUOTEX_PASSWORD in .env", "ERROR")
            return None

        log_action(f"Connecting to Quotex ({account_type})...", "INFO")

        # Initialize Quotex connection
        quotex = Quotex(email, password)

        # Connect to Quotex
        check_connect, reason = await quotex.connect()

        if not check_connect:
            log_action(f"Failed to connect to Quotex: {reason}", "ERROR")
            return None

        log_action("Successfully connected to Quotex", "SUCCESS")

        # Set account type
        if account_type == "quotex_demo":
            await quotex.change_balance("PRACTICE")  # Demo account
            balance = await quotex.get_balance()
            log_action(f"Connected to Quotex Demo account - Balance: ${balance}", "SUCCESS")
        else:  # quotex_live
            await quotex.change_balance("REAL")  # Live account
            balance = await quotex.get_balance()
            log_action(f"Connected to Quotex Live account - Balance: ${balance}", "SUCCESS")

        # Check balance
        if balance <= 0:
            log_action(f"Account balance is ${balance} - switching to practice mode", "WARNING")
            return None

        return quotex

    except ImportError:
        log_action("quotexpy not installed. Run: pip install quotexpy", "ERROR")
        return None
    except Exception as e:
        log_action(f"Failed to connect to Quotex: {str(e)}", "ERROR")
        return None

async def place_trade(signal, pair, account_type, quotex_connection=None, amount=10):
    """
    Place trade based on signal and account type using quotexpy
    """
    if signal == "no signal":
        return False

    if account_type == "practice":
        log_action(f"PRACTICE SIGNAL: {signal.upper()} on {pair} (${amount})", "SIGNAL")
        return True

    if quotex_connection is None:
        log_action("No Quotex connection available - switching to practice mode", "WARNING")
        log_action(f"PRACTICE SIGNAL: {signal.upper()} on {pair} (${amount})", "SIGNAL")
        return True

    try:
        # Check balance before placing trade
        balance = await quotex_connection.get_balance()
        if balance < amount:
            log_action(f"Insufficient balance: ${balance} < ${amount} - switching to practice mode", "WARNING")
            log_action(f"PRACTICE SIGNAL: {signal.upper()} on {pair} (${amount})", "SIGNAL")
            return True

        # Convert signal to Quotex format
        direction = "call" if signal == "call" else "put"

        log_action(f"Placing {direction.upper()} trade on {pair} (${amount}) - Balance: ${balance}", "WARNING")

        # Place trade using quotexpy
        result = await quotex_connection.buy(
            amount=amount,
            asset=pair,
            direction=direction,
            duration=60  # 1 minute
        )

        if result:
            new_balance = await quotex_connection.get_balance()
            log_action(f"Trade placed successfully: {direction.upper()} {pair} - New Balance: ${new_balance}", "SUCCESS")
            return True
        else:
            log_action(f"Failed to place trade: {direction.upper()} {pair}", "ERROR")
            return False

    except Exception as e:
        log_action(f"Error placing trade: {str(e)}", "ERROR")
        log_action(f"PRACTICE SIGNAL: {signal.upper()} on {pair} (${amount})", "SIGNAL")
        return False

def calculate_next_scan_time():
    """Calculate the next scan time (58 seconds after current minute)"""
    now = datetime.now()
    next_minute = now.replace(second=58, microsecond=0)
    
    # If we're past 58 seconds, move to next minute
    if now.second >= 58:
        next_minute += timedelta(minutes=1)
    
    return next_minute

def is_valid_trade_time():
    """Check if current time is within 1 second after candle open"""
    now = datetime.now()
    seconds_after_minute = now.second
    
    # Valid trade window: 0-1 seconds after minute start
    return 0 <= seconds_after_minute <= 1

async def trading_loop(pair, timeframe, strategy, account_type, quotex_connection, trade_amount):
    """Main trading loop"""
    log_action("Starting trading loop...", "INFO")
    log_action(f"Pair: {pair}, Timeframe: {timeframe}, Strategy: {strategy}", "INFO")
    log_action(f"Account: {account_type}, Amount: ${trade_amount}", "INFO")
    
    while True:
        try:
            # Calculate next scan time
            next_scan = calculate_next_scan_time()
            now = datetime.now()
            
            # Wait until scan time
            wait_seconds = (next_scan - now).total_seconds()
            if wait_seconds > 0:
                log_action(f"Next scan in {int(wait_seconds)} seconds at {next_scan.strftime('%H:%M:%S')}", "INFO")
                await asyncio.sleep(wait_seconds)
            
            # Fetch and analyze data (simulate)
            log_action(f"Analyzing {pair} with {strategy}...", "INFO")
            signal, confidence = get_signal(pair, timeframe, strategy)
            
            if signal != "no signal":
                log_action(f"Signal detected: {signal.upper()} (confidence: {confidence:.1%})", "SIGNAL")
                
                # Check if we're in valid trade window
                if is_valid_trade_time():
                    success = await place_trade(signal, pair, account_type, quotex_connection)
                    if success:
                        log_action("Trade executed successfully", "SUCCESS")
                    else:
                        log_action("Trade execution failed", "ERROR")
                else:
                    log_action("Signal detected but outside valid trade window - skipping", "WARNING")
            else:
                log_action("No signal detected", "INFO")
            
            # Wait a bit before next iteration
            await asyncio.sleep(5)
            
        except KeyboardInterrupt:
            log_action("Trading loop stopped by user", "WARNING")
            break
        except Exception as e:
            log_action(f"Error in trading loop: {str(e)}", "ERROR")
            await asyncio.sleep(10)  # Wait before retrying

async def main():
    """Main function"""
    print_colored("\n🚀 QUOTEX TRADING BOT", "cyan")
    print_colored("=" * 50, "blue")

    try:
        # User selections
        pair = select_pair()
        timeframe = select_timeframe()
        strategy = select_strategy()
        account_type = select_account_type()

        # Select trade amount
        trade_amount = select_trade_amount()

        # Connect to Quotex if needed
        quotex_connection = await connect_to_quotex(account_type)

        if account_type != "practice" and quotex_connection is None:
            print_colored("⚠️  No Quotex connection - switching to practice mode", "yellow")
            account_type = "practice"

        # Display configuration
        print_colored("\n📋 Trading Configuration:", "cyan")
        print_colored(f"   Pair: {pair}", "green")
        print_colored(f"   Timeframe: {timeframe}", "green")
        print_colored(f"   Strategy: {strategy}", "green")
        print_colored(f"   Account: {account_type}", "green")
        print_colored(f"   Amount: ${trade_amount}", "green")

        # Start trading loop
        print_colored("\n🎯 Starting trading bot...", "green")
        print_colored("Press Ctrl+C to stop", "yellow")

        await trading_loop(pair, timeframe, strategy, account_type, quotex_connection, trade_amount)

    except KeyboardInterrupt:
        print_colored("\n👋 Trading bot stopped by user", "yellow")
    except Exception as e:
        print_colored(f"\n❌ Error: {str(e)}", "red")

if __name__ == "__main__":
    asyncio.run(main())
