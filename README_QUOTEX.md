# 🚀 Quotex Trading Bot

An advanced automated trading bot for Quotex platform with live signal generation, precise timing, and multiple account support.

## ✨ Features

- **🎯 Precise Timing**: Analyzes market data between :58-:04 seconds, trades within 1 second of candle open
- **📊 Multiple Pairs**: 60+ trading pairs including Forex, Crypto, Commodities, Indices, and OTC
- **🔄 Multiple Timeframes**: 1m, 5m, 15m, 30m, 1h, 4h, 1d
- **🎲 12 Trading Strategies**: From momentum breakout to harmonic patterns
- **💼 Account Types**: Practice (signals only), Demo trading, Live trading
- **🔐 Secure**: Environment variables for credentials
- **⚡ Async**: Non-blocking operations for optimal performance

## 🛠️ Installation

### 1. Install Dependencies
```bash
pip install -r requirements_quotex.txt
```

### 2. Install pyquotex
```bash
pip install pyquotex
```

### 3. Setup Environment Variables
Copy `.env.example` to `.env` and add your credentials:
```bash
cp .env.example .env
```

Edit `.env` file:
```env
QUOTEX_EMAIL=<EMAIL>
QUOTEX_PASSWORD=your_password
```

## 🚀 Quick Start

### Run the Trading Bot
```bash
python quotex_trading_bot.py
```

### Follow the Interactive Setup:
1. **Select Trading Pair** (60+ options displayed in 4 columns)
2. **Choose Timeframe** (1m to 1d)
3. **Pick Strategy** (12 available strategies)
4. **Select Account Type**:
   - Practice: Signal display only
   - Demo: Trade on demo account
   - Live: Trade with real money (requires confirmation)

## 📊 Available Trading Pairs

### Forex (16 pairs)
- Major: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, USDCHF, NZDUSD
- Cross: EURJPY, GBPJPY, EURGBP, AUDJPY, EURAUD, GBPAUD, etc.

### Cryptocurrency (16 pairs)
- BTCUSD, ETHUSD, LTCUSD, XRPUSD, ADAUSD, DOTUSD, LINKUSD, etc.

### Commodities (8 pairs)
- XAUUSD (Gold), XAGUSD (Silver), WTIUSD (Oil), BRENTUSD, NATGAS, etc.

### Indices (16 pairs)
- SPX500, NAS100, US30, GER30, UK100, FRA40, JPN225, etc.

### OTC Pairs (Available 24/7)
- EURUSD_OTC, GBPUSD_OTC, XAUUSD_OTC, SPX500_OTC, etc.

## 🎯 Trading Strategies

1. **momentum_breakout** - Breakout trading with momentum confirmation
2. **support_resistance** - Key level bounce/break strategies
3. **trend_following** - Trend continuation patterns
4. **reversal_pattern** - Counter-trend reversal signals
5. **volume_spike** - Volume-based momentum trades
6. **fibonacci_retracement** - Fibonacci level trading
7. **bollinger_bands** - Bollinger band squeeze/expansion
8. **macd_divergence** - MACD divergence signals
9. **rsi_oversold** - RSI extreme level trading
10. **moving_average_cross** - MA crossover signals
11. **price_action** - Pure price action patterns
12. **harmonic_pattern** - Harmonic pattern recognition

## ⏰ Timing System

### Signal Generation Window
- **Analysis Period**: :58 to :04 seconds of each minute
- **Example**: 12:34:58 to 12:35:04

### Trade Execution Window
- **Valid Window**: 0-1 seconds after candle open
- **Example**: 12:35:00 to 12:35:01

### Timing Logic
```
12:34:58 ──► Start Analysis
12:34:59 ──► Continue Analysis
12:35:00 ──► New Candle Opens
12:35:01 ──► Last Valid Trade Time
12:35:02 ──► Too Late - Skip Trade
```

## 💼 Account Types

### 1. Practice Mode
- **Purpose**: Signal testing and strategy validation
- **Action**: Display signals only, no actual trades
- **Risk**: None
- **Connection**: No Quotex connection required

### 2. Demo Trading
- **Purpose**: Test with virtual money
- **Action**: Place trades on Quotex demo account
- **Risk**: Virtual money only
- **Connection**: Quotex demo account

### 3. Live Trading
- **Purpose**: Real money trading
- **Action**: Place trades on Quotex live account
- **Risk**: Real money at risk
- **Connection**: Quotex live account
- **Confirmation**: Requires typing 'CONFIRM'

## 🔐 Security Features

### Environment Variables
- Credentials stored in `.env` file
- Never hardcoded in source code
- `.env` excluded from version control

### Safe Defaults
- Practice mode as default
- Confirmation required for live trading
- Error handling for connection failures

## 📝 Logging System

All actions are logged with timestamps:
```
[2024-01-15 14:35:02] INFO: Analyzing EURUSD with momentum_breakout...
[2024-01-15 14:35:03] SIGNAL: Signal detected: CALL (confidence: 78.5%)
[2024-01-15 14:35:03] SUCCESS: Trade executed successfully
```

## 🔧 Configuration

### Default Settings
- Trade Amount: $10
- Trade Duration: 1 minute
- Max Retries: 3
- Connection Timeout: 30 seconds

### Customization
Modify these variables in the script:
```python
amount = 10  # Trade amount in USD
duration = 60  # Trade duration in seconds
```

## ⚠️ Risk Disclaimer

- **High Risk**: Binary options trading involves substantial risk
- **Demo First**: Always test strategies on demo account
- **Risk Management**: Never risk more than you can afford to lose
- **No Guarantee**: Past performance doesn't guarantee future results

## 🐛 Troubleshooting

### Common Issues

1. **pyquotex not found**
   ```bash
   pip install pyquotex
   ```

2. **Credentials error**
   - Check `.env` file exists
   - Verify email/password are correct
   - Ensure no extra spaces in credentials

3. **Connection timeout**
   - Check internet connection
   - Verify Quotex server status
   - Try demo account first

4. **No signals generated**
   - Normal behavior - not all strategies generate signals constantly
   - Try different strategy or timeframe
   - Check if market is open for selected pair

## 📞 Support

For issues or questions:
1. Check troubleshooting section
2. Verify all requirements are installed
3. Test with practice mode first
4. Review logs for error messages

## 📄 License

This project is for educational purposes. Use at your own risk.

---

**⚡ Happy Trading! ⚡**
