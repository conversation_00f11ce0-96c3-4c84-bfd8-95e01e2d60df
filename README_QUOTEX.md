# 🚀 Quotex Trading Bot System

A comprehensive trading bot that integrates with Quotex using the quotexpy library. This system provides rule-based trading strategies with signal generation, automated execution, and multiple account modes.

## ✨ Features

### 🎯 **Trading Modes**
- **📊 Practice Mode**: Signal display only (no real trading)
- **🎯 Demo Mode**: Demo trading with virtual money
- **💰 Live Mode**: Real money trading (requires confirmation)

### 💱 **Supported Assets**
- **OTC Pairs (24/7)**: EURUSD_otc, GBPUSD_otc, USDJPY_otc, AUDUSD_otc, and 25+ more
- **Live Pairs**: EURUSD, GBPUSD, USDJPY, AUDUSD, and 26+ more
- **Commodities**: Gold (XAUUSD), Silver (XAGUSD), Oil (UKBrent, USCrude)
- **Indices**: DJIUSD, SPXUSD, NDXUSD, and more
- **Stocks**: AXP, BA, FB, INTC, JNJ, MCD, MSFT, PFE

### 🎯 **Advanced Strategies**
1. **S1**: Breakout with Volume (70-80% accuracy)
2. **S2**: Pullback Entry Strategy (88% accuracy)
3. **S3**: Support/Resistance Rejection (70-80% accuracy)
4. **S4**: Trendline Break with Rejection (65-75% accuracy)
5. **S5**: Pre-Candle Momentum Strategy (75-85% accuracy)
6. **S6**: Pre-Candle Reversal Strategy (80-90% accuracy)
7. **S7**: Pre-Candle Breakout Strategy (75-85% accuracy)
8. **S8**: Pre-Candle Fakeout Strategy (85%+ accuracy)
9. **S9**: Ranging Market Strategy (75-85% accuracy)
10. **S10**: Trending Market Strategy (78-88% accuracy)

### ⏰ **Timeframes**
- 1 Minute, 2 Minutes, 5 Minutes, 10 Minutes
- 15 Minutes, 30 Minutes, 1 Hour

### 🛡️ **Safety Features**
- **Balance Protection**: Auto-switch to practice mode if balance is zero
- **Trade Amount Validation**: $1 minimum, $1000 maximum
- **Connection Error Handling**: Graceful error recovery
- **User Confirmation**: Required for live trading mode

## 🚀 Quick Start

### 1. **Installation**
```bash
# The quotexpy library is included in the project
# Install basic requirements:
pip install pandas numpy requests
```

### 2. **Run the Bot**
```bash
python trading_bot_launcher.py
```

### 3. **Select Mode**
```
🚀 QUOTEX TRADING BOT SYSTEM
Choose an option:

1. 📊 Practice (Signal display only)
2. 🎯 Quotex Demo (Demo trading)  
3. 💰 Quotex Live (Live trading)
4. ❌ Exit
```

## 📊 **Usage Guide**

### **Practice Mode (Recommended for Testing)**
- No real money involved
- Displays trading signals only
- Perfect for strategy testing
- No Quotex connection required

### **Demo Mode**
- Uses Quotex demo account
- Virtual money trading
- Full trading functionality
- Requires Quotex connection

### **Live Mode**
- Real money trading
- Requires explicit confirmation
- Full balance protection
- Auto-fallback to practice if balance is zero

## 🎯 **Asset Selection**

The bot displays assets in 4 columns for easy selection:

```
📊 OTC Pairs (24/7 Available):
================================================================================
 1. EURUSD_otc         2. GBPUSD_otc         3. USDJPY_otc         4. AUDUSD_otc        
 5. USDCAD_otc         6. USDCHF_otc         7. AUDCAD_otc         8. AUDCHF_otc        
...

🌍 Live Pairs (Market Hours):
================================================================================
 1. EURUSD             2. GBPUSD             3. USDJPY             4. AUDUSD            
 5. USDCAD             6. USDCHF             7. NZDUSD             8. EURAUD            
...
```

## 📈 **Live Trading Display**

```
🕐 TIME         | 💱 ASSET           | 📈📉 SIGNAL   | 🎯 CONF   | 💰 PRICE      | 🔧 STRATEGY   | 📊 STATUS
====================================================================================================
🕐 17:30:50     | 💱 EURUSD_otc      | 📈 CALL       | 🎯 75.2%  | 💰 1.10350    | 🔧 S1         | ✅ EXECUTED
🕐 17:31:20     | 💱 EURUSD_otc      | ⚪ HOLD       | 🎯 -      | 💰 1.10355    | 🔧 S1         | 📊 SIGNAL ONLY
```

## ⚙️ **Configuration**

### **Credentials** (Built-in)
```python
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"
```

### **Supported Assets** (Auto-detected from quotexpy)
- All assets are loaded from `quotexpy.constants.codes_asset`
- Only supported pairs are displayed
- Automatic format conversion for compatibility

## 🛠️ **Technical Details**

### **Architecture**
- **Single Launcher**: `trading_bot_launcher.py` handles everything
- **Strategy Engine**: Integrated from existing `strategy_engine.py`
- **Quotex Integration**: Uses included `quotexpy` library
- **Color System**: Consistent with existing bot design

### **Signal Generation**
- Real-time technical analysis
- Multiple strategy evaluation
- Confidence scoring
- Price action analysis

### **Trade Execution**
- Async trade placement
- Balance verification
- Error handling and retry logic
- Trade result monitoring

## 🚨 **Important Notes**

### **Connection Issues**
If you see connection errors:
```
❌ Connection error: net::ERR_CONNECTION_RESET
```
This is normal and can happen due to:
- Network connectivity issues
- Quotex server maintenance
- Rate limiting
- Firewall restrictions

### **Balance Management**
- Bot automatically checks balance before each trade
- Switches to practice mode if balance is insufficient
- Displays current balance in real-time

### **Risk Management**
- Minimum trade amount: $1
- Maximum trade amount: $1000
- Confidence threshold: 60% minimum
- Auto-stop on zero balance

## 📁 **File Structure**

```
📦 Quotex Trading Bot
├── 🚀 trading_bot_launcher.py    # Main launcher (RUN THIS)
├── 📊 strategy_engine.py         # Trading strategies
├── 🛠️ utils.py                   # Utility functions
├── ⚙️ config.py                  # Configuration
├── 📁 quotexpy/                  # Quotex API library
│   ├── __init__.py
│   ├── api.py
│   ├── constants.py
│   └── ...
├── 📄 requirements_quotex.txt    # Dependencies
└── 📖 README_QUOTEX_NEW.md      # This file
```

## 🎯 **Strategy Descriptions**

Each strategy has detailed descriptions and accuracy ratings:
- **Volume-based strategies** for volatile markets
- **Trend-following strategies** for trending markets  
- **Reversal strategies** for ranging markets
- **Breakout strategies** for high volatility
- **Support/Resistance strategies** for technical levels

## ⚠️ **Disclaimer**

**IMPORTANT**: This bot is for educational and testing purposes. Trading involves significant risk and you should:

- ✅ **Test thoroughly** in practice mode first
- ✅ **Start with small amounts** in demo mode
- ✅ **Never trade money** you cannot afford to lose
- ✅ **Understand the risks** of automated trading
- ✅ **Monitor the bot** during operation

**The developers are not responsible for any trading losses.**

## 🆘 **Support**

For issues or questions:
1. Check the console output for error messages
2. Try practice mode first to test functionality
3. Verify your internet connection
4. Ensure Quotex account is accessible

---

**Happy Trading! 🚀📈**
