#!/usr/bin/env python3
"""
Trading Bot Launcher
Main entry point for the trading bot system
"""

import sys
import os
from utils import print_colored, print_header, select_currency_pairs, select_timeframe, select_strategies

def show_menu():
    """Display the main menu"""
    print_header("🤖 ADVANCED TRADING BOT SYSTEM")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 📈 Rule-Based Trading (Live)", "BUY", bold=True)
    print_colored("2. 🔬 Advanced Signal Generator", "HEADER", bold=True)
    print_colored("3. 🔗 Test API Connection", "INFO", bold=True)
    print_colored("4. 📊 View Current Directory", "INFO", bold=True)
    print_colored("5. ❌ Exit", "ERROR", bold=True)
    print()

def test_api_connection():
    """Test the Oanda API connection"""
    print_header("🔗 TESTING API CONNECTION")
    try:
        import test_oanda_connection
        # The test will run automatically when imported
    except Exception as e:
        print_colored(f"❌ Error testing API: {str(e)}", "ERROR")

def start_rule_based_trading():
    """Start the rule-based live trading bot"""
    print_header("📈 RULE-BASED LIVE TRADING")
    print_colored("🔧 Using: Pure rule-based strategies (S1-S10)", "INFO")
    print_colored("📊 Signal Generation: Hard-coded technical analysis", "INFO")
    print_colored("⚡ Performance: Fast and transparent", "SUCCESS")
    print_colored("⚠️  This will start live market monitoring", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()

    # Select currency pairs
    try:
        selected_pairs = select_currency_pairs()
        print()

        # Select timeframe
        selected_timeframe = select_timeframe()
        if selected_timeframe is None:
            print_colored("❌ Timeframe selection cancelled", "WARNING")
            return
        print()

        # Select strategies
        selected_strategies = select_strategies()
        if not selected_strategies:
            print_colored("❌ No strategies selected", "ERROR")
            return
        print()

        print_colored(f"📊 Selected {len(selected_pairs)} currency pairs for analysis", "INFO")
        print_colored(f"🎯 Selected {len(selected_strategies)} strategies for trading", "INFO")
        print_colored("⚠️  Ready to start live market monitoring", "WARNING", bold=True)
        print()

        confirm = input("Start rule-based live trading? (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                from live_trading_bot import main
                main(selected_pairs, selected_timeframe, selected_strategies)
            except Exception as e:
                print_colored(f"❌ Error starting rule-based trading: {str(e)}", "ERROR")
        else:
            print_colored("❌ Rule-based trading cancelled", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in selection: {str(e)}", "ERROR")



def start_advanced_signal_generator():
    """Start the advanced signal generator with historical pattern recognition"""
    print_header("🔬 ADVANCED SIGNAL GENERATOR")
    print_colored("🎯 Using: Historical Pattern Recognition", "HEADER")
    print_colored("📊 Analysis: Time-based candle pattern analysis", "HEADER")
    print_colored("🔍 Filters: Trend confirmation + Market structure", "HEADER")
    print_colored("⚡ Performance: Advanced signal scoring system", "SUCCESS")
    print()

    try:
        from advanced_signal_generator import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting advanced signal generator: {str(e)}", "ERROR")



def view_directory():
    """View current directory contents"""
    print_header("📊 CURRENT DIRECTORY CONTENTS")
    
    try:
        files = os.listdir('.')
        
        # Separate files by type
        python_files = [f for f in files if f.endswith('.py')]
        directories = [f for f in files if os.path.isdir(f)]
        other_files = [f for f in files if f not in python_files + directories]

        if python_files:
            print_colored("🐍 Python Files:", "INFO", bold=True)
            for f in sorted(python_files):
                print_colored(f"   {f}", "INFO")
            print()
        
        if directories:
            print_colored("📁 Directories:", "HEADER", bold=True)
            for d in sorted(directories):
                print_colored(f"   {d}/", "HEADER")
            print()
        
        if other_files:
            print_colored("📄 Other Files:", "WARNING", bold=True)
            for f in sorted(other_files):
                print_colored(f"   {f}", "WARNING")
            print()
        
        print_colored(f"Total files: {len(files)}", "INFO", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Error viewing directory: {str(e)}", "ERROR")

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-5): ").strip()

            if choice == '1':
                start_rule_based_trading()
            elif choice == '2':
                start_advanced_signal_generator()
            elif choice == '3':
                test_api_connection()
            elif choice == '4':
                view_directory()
            elif choice == '5':
                print_colored("👋 Goodbye! ✨ Hope Your Section Was Good", "INFO", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3', '4']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! ✨ Hope Your Section Was Good", "INFO", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
