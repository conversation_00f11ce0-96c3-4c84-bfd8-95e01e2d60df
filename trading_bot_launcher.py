#!/usr/bin/env python3
"""
Quotex Trading Bot Launcher
Comprehensive trading bot with Quotex integration using quotexpy
"""

import sys
import os
import time
import asyncio
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Import quotexpy for Quotex integration
try:
    from quotexpy import Quotex
    from quotexpy.constants import codes_asset
    QUOTEX_AVAILABLE = True
except ImportError:
    print("❌ quotexpy not found. Please install: pip install quotexpy")
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG

# Quotex credentials
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"

# Quotex supported assets (from quotexpy constants)
QUOTEX_OTC_PAIRS = [
    "EURUSD_otc", "GBPUSD_otc", "USDJPY_otc", "AUDUSD_otc", "USDCAD_otc", "USDCHF_otc",
    "AUDCAD_otc", "AUDCHF_otc", "AUDJPY_otc", "CADJPY_otc", "EURCHF_otc", "EURGBP_otc",
    "EURJPY_otc", "GBPAUD_otc", "GBPJPY_otc", "NZDJPY_otc", "NZDUSD_otc", "XAGUSD_otc",
    "XAUUSD_otc", "UKBrent_otc", "USCrude_otc", "AXP_otc", "BA_otc", "FB_otc", "INTC_otc",
    "JNJ_otc", "MCD_otc", "MSFT_otc", "PFE_otc"
]

QUOTEX_LIVE_PAIRS = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURAUD",
    "EURCAD", "EURCHF", "EURGBP", "EURJPY", "EURSGD", "GBPAUD", "GBPCAD", "GBPCHF",
    "GBPJPY", "XAGUSD", "XAUUSD", "DJIUSD", "F40EUR", "FTSGBP", "GEREUR", "HSIHKD",
    "IBXEUR", "IT4EUR", "JPXJPY", "NDXUSD", "SPXUSD", "STXEUR"
]

# Available timeframes for Quotex
QUOTEX_TIMEFRAMES = {
    "1": {"name": "1 Minute", "seconds": 60},
    "2": {"name": "2 Minutes", "seconds": 120},
    "5": {"name": "5 Minutes", "seconds": 300},
    "10": {"name": "10 Minutes", "seconds": 600},
    "15": {"name": "15 Minutes", "seconds": 900},
    "30": {"name": "30 Minutes", "seconds": 1800},
    "60": {"name": "1 Hour", "seconds": 3600}
}

# Global Quotex client
quotex_client = None

def show_menu():
    """Display the main menu"""
    print_header("🚀 QUOTEX TRADING BOT SYSTEM")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "INFO", bold=True)
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "WARNING", bold=True)
    print_colored("3. 💰 Quotex Live (Live trading)", "ERROR", bold=True)
    print_colored("4. ❌ Exit", "ERROR", bold=True)
    print()

async def connect_to_quotex(account_type="PRACTICE"):
    """Connect to Quotex with specified account type"""
    global quotex_client

    if not QUOTEX_AVAILABLE:
        print_colored("❌ quotexpy not installed. Run: pip install quotexpy", "ERROR")
        return False

    try:
        print_colored("🔄 Connecting to Quotex...", "INFO")
        quotex_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)

        # Connect to Quotex
        connected = await quotex_client.connect()
        if not connected:
            print_colored("❌ Failed to connect to Quotex", "ERROR")
            return False

        # Change account type
        quotex_client.change_account(account_type)

        # Get instruments
        await quotex_client.get_instruments()

        print_colored("✅ Successfully connected to Quotex", "SUCCESS")
        return True

    except Exception as e:
        print_colored(f"❌ Connection error: {str(e)}", "ERROR")
        return False

async def check_balance():
    """Check current account balance"""
    if not quotex_client:
        return 0

    try:
        balance = await quotex_client.get_balance()
        return balance if balance else 0
    except Exception as e:
        print_colored(f"❌ Error checking balance: {str(e)}", "ERROR")
        return 0

def display_pairs_in_columns(pairs, title, columns=4):
    """Display trading pairs in specified number of columns"""
    print_colored(f"\n{title}", "HEADER", bold=True)
    print_colored("=" * 80, "HEADER")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{i+j+1:2d}. {pair:<18}"
        print_colored(formatted_row, "INFO")

    print_colored("=" * 80, "HEADER")

def select_trading_pair():
    """Select trading pair from available options"""
    print_header("💱 ASSET SELECTION")

    # Display OTC pairs
    display_pairs_in_columns(QUOTEX_OTC_PAIRS, "📊 OTC Pairs (24/7 Available):")

    # Display Live pairs
    display_pairs_in_columns(QUOTEX_LIVE_PAIRS, "🌍 Live Pairs (Market Hours):", columns=4)

    total_pairs = len(QUOTEX_OTC_PAIRS) + len(QUOTEX_LIVE_PAIRS)
    all_pairs = QUOTEX_OTC_PAIRS + QUOTEX_LIVE_PAIRS

    while True:
        try:
            choice = input(f"\nSelect asset (1-{total_pairs}): ").strip()
            if not choice:
                return None

            choice = int(choice)
            if 1 <= choice <= total_pairs:
                selected_pair = all_pairs[choice - 1]
                print_colored(f"✅ Selected: {selected_pair}", "SUCCESS")
                return selected_pair
            else:
                print_colored(f"❌ Please enter a number between 1 and {total_pairs}", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_timeframe():
    """Select trading timeframe"""
    print_header("⏰ TIMEFRAME SELECTION")

    print_colored("Available timeframes:", "INFO", bold=True)
    for key, info in QUOTEX_TIMEFRAMES.items():
        print_colored(f"{key}. {info['name']}", "INFO")

    while True:
        try:
            choice = input("\nSelect timeframe (1-7): ").strip()
            if choice in QUOTEX_TIMEFRAMES:
                selected = QUOTEX_TIMEFRAMES[choice]
                print_colored(f"✅ Selected: {selected['name']}", "SUCCESS")
                return int(choice) * 60  # Return duration in seconds
            else:
                print_colored("❌ Please enter a valid timeframe number", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None



def select_trade_amount():
    """Select trade amount"""
    print_header("💰 TRADE AMOUNT SELECTION")

    print_colored("Enter trade amount (e.g., 1, 2, 5, 10):", "INFO", bold=True)
    print_colored("💡 Minimum: $1, Recommended: $1-$10 for demo", "WARNING")

    while True:
        try:
            amount = input("\nTrade amount ($): ").strip()
            if not amount:
                return None

            amount = float(amount)
            if amount < 1:
                print_colored("❌ Minimum trade amount is $1", "ERROR")
                continue
            elif amount > 1000:
                print_colored("❌ Maximum trade amount is $1000", "ERROR")
                continue

            print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
            return amount

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_strategies():
    """Select trading strategies"""
    print_header("🎯 STRATEGY SELECTION")

    print_colored("Available strategies:", "INFO", bold=True)
    strategies = list(STRATEGY_CONFIG.keys())

    for i, strategy_id in enumerate(strategies, 1):
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"{i}. {strategy_id}: {strategy_info['name']}", "BUY", bold=True)
        if 'description' in strategy_info:
            print_colored(f"   📝 {strategy_info['description']}", "INFO")
        if 'accuracy' in strategy_info:
            print_colored(f"   🎯 Accuracy: {strategy_info['accuracy']}", "SUCCESS")
        print()

    print_colored("💡 You can select multiple strategies (e.g., 1,3,5 or 'all')", "WARNING")

    while True:
        try:
            selection = input("\nEnter strategy numbers: ").strip().lower()

            if selection == 'all':
                selected_strategies = strategies
                break

            # Parse selection
            selected_strategies = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                num = int(part)
                if 1 <= num <= len(strategies):
                    selected_strategies.append(strategies[num-1])
                else:
                    raise ValueError(f"Invalid strategy number: {num}")

            if selected_strategies:
                break
            else:
                print_colored("❌ Please select at least one strategy", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected strategies
    print_colored(f"\n✅ Selected {len(selected_strategies)} strategies:", "SUCCESS", bold=True)
    for strategy_id in selected_strategies:
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"   • {strategy_id}: {strategy_info['name']}", "SUCCESS")

    return selected_strategies



async def execute_trade(asset, action, amount, duration):
    """Execute trade on Quotex"""
    if not quotex_client:
        print_colored("❌ Not connected to Quotex", "ERROR")
        return False, "Not connected"

    try:
        # Check if asset is available
        asset_info = quotex_client.check_asset_open(asset)
        if not asset_info or not asset_info[2]:  # asset_info[2] is the open status
            print_colored(f"❌ Asset {asset} is not available for trading", "ERROR")
            return False, "Asset not available"

        # Execute trade
        success, trade_info = await quotex_client.trade(action, amount, asset, duration)

        if success:
            print_colored(f"✅ Trade executed: {action} {asset} ${amount}", "SUCCESS")
            return True, trade_info
        else:
            print_colored(f"❌ Trade failed: {trade_info}", "ERROR")
            return False, trade_info

    except Exception as e:
        print_colored(f"❌ Trade execution error: {str(e)}", "ERROR")
        return False, str(e)

def convert_pair_format(pair):
    """Convert pair format for quotexpy compatibility"""
    # Remove _otc suffix for processing
    base_pair = pair.replace("_otc", "")

    # Map common pairs
    pair_mapping = {
        "EUR_USD": "EURUSD",
        "GBP_USD": "GBPUSD",
        "USD_JPY": "USDJPY",
        "AUD_USD": "AUDUSD",
        "USD_CAD": "USDCAD",
        "USD_CHF": "USDCHF",
        "NZD_USD": "NZDUSD",
        "EUR_GBP": "EURGBP",
        "EUR_JPY": "EURJPY",
        "GBP_JPY": "GBPJPY",
        "XAU_USD": "XAUUSD",
        "XAG_USD": "XAGUSD"
    }

    # Convert if mapping exists
    if base_pair in pair_mapping:
        converted = pair_mapping[base_pair]
        # Add back _otc suffix if original had it
        if "_otc" in pair:
            converted += "_otc"
        return converted

    # Return as-is if no mapping needed
    return pair

async def generate_signal(asset, strategy_engine):
    """Generate trading signal for asset"""
    try:
        # Convert asset format for data fetching
        converted_asset = convert_pair_format(asset)

        # For demo purposes, create sample data
        # In real implementation, you would fetch actual candle data from Quotex
        sample_data = {
            'open': [1.1000, 1.1010, 1.1020, 1.1015, 1.1025],
            'high': [1.1015, 1.1025, 1.1035, 1.1030, 1.1040],
            'low': [1.0995, 1.1005, 1.1015, 1.1010, 1.1020],
            'close': [1.1010, 1.1020, 1.1015, 1.1025, 1.1035],
            'volume': [1000, 1200, 800, 1500, 1100]
        }

        df = pd.DataFrame(sample_data)

        # Add technical indicators (simplified)
        df['rsi'] = 50 + np.random.randn(len(df)) * 10  # Mock RSI
        df['sma_20'] = df['close'].rolling(window=min(3, len(df))).mean()
        df['ema_12'] = df['close'].ewm(span=min(3, len(df))).mean()
        df['ema_26'] = df['close'].ewm(span=min(3, len(df))).mean()

        # Generate signal using strategy engine
        signal, confidence = strategy_engine.evaluate_strategy_1(df)

        current_price = df['close'].iloc[-1]

        if signal == 1:
            return "call", confidence, current_price
        elif signal == -1:
            return "put", confidence, current_price
        else:
            return "hold", confidence, current_price

    except Exception as e:
        print_colored(f"❌ Signal generation error: {str(e)}", "ERROR")
        return "hold", 0.0, 0.0

async def run_trading_bot(account_type, is_practice_only=False):
    """Main trading bot execution"""
    print_header(f"🚀 QUOTEX TRADING BOT - {account_type.upper()} MODE")

    # Connect to Quotex
    if not is_practice_only:
        connected = await connect_to_quotex(account_type)
        if not connected:
            print_colored("❌ Cannot proceed without Quotex connection", "ERROR")
            return

        # Check balance
        balance = await check_balance()
        print_colored(f"💰 Current balance: ${balance:.2f}", "SUCCESS" if balance > 0 else "ERROR")

        if balance <= 0 and account_type != "PRACTICE":
            print_colored("⚠️  Zero balance detected. Switching to practice mode...", "WARNING")
            quotex_client.change_account("PRACTICE")
            account_type = "PRACTICE"

    # Asset selection
    selected_asset = select_trading_pair()
    if not selected_asset:
        return

    # Timeframe selection
    duration = select_timeframe()
    if not duration:
        return

    # Trade amount selection
    if not is_practice_only:
        trade_amount = select_trade_amount()
        if not trade_amount:
            return
    else:
        trade_amount = 1  # Default for practice mode

    # Strategy selection
    selected_strategies = select_strategies()
    if not selected_strategies:
        return

    # Initialize strategy engine
    strategy_engine = StrategyEngine()

    print_header("📊 LIVE TRADING SIGNALS")
    print_colored("Press Ctrl+C to stop the bot", "WARNING", bold=True)
    print()

    # Print signal table header
    header_line = (
        f"🕐 {'TIME':<12} | "
        f"💱 {'ASSET':<15} | "
        f"📈📉 {'SIGNAL':<8} | "
        f"🎯 {'CONF':<6} | "
        f"💰 {'PRICE':<10} | "
        f"🔧 {'STRATEGY':<10} | "
        f"📊 {'STATUS':<15}"
    )
    print_colored("=" * 100, "HEADER")
    print_colored(header_line, "HEADER", bold=True)
    print_colored("=" * 100, "HEADER")

    try:
        while True:
            current_time = datetime.now().strftime('%H:%M:%S')

            # Generate signal
            signal, confidence, price = await generate_signal(selected_asset, strategy_engine)

            # Determine signal color and display
            if signal == "call":
                signal_display = "📈 CALL"
                signal_color = "BUY"
            elif signal == "put":
                signal_display = "📉 PUT"
                signal_color = "SELL"
            else:
                signal_display = "⚪ HOLD"
                signal_color = "HOLD"

            # Format confidence
            conf_display = f"{confidence*100:.1f}%" if confidence > 0 else "-"

            # Execute trade if not practice mode and signal is valid
            trade_status = "SIGNAL ONLY"
            if not is_practice_only and signal in ["call", "put"] and confidence > 0.6:
                # Check balance before trading
                current_balance = await check_balance()
                if current_balance >= trade_amount:
                    success, trade_info = await execute_trade(selected_asset, signal, trade_amount, duration)
                    trade_status = "✅ EXECUTED" if success else "❌ FAILED"
                else:
                    trade_status = "💰 LOW BALANCE"
                    print_colored("⚠️  Insufficient balance. Switching to practice mode...", "WARNING")
                    quotex_client.change_account("PRACTICE")
                    is_practice_only = True

            # Display signal row
            row_line = (
                f"🕐 {current_time:<12} | "
                f"💱 {selected_asset:<15} | "
                f"{signal_display:<10} | "
                f"🎯 {conf_display:<6} | "
                f"💰 {price:<10.5f} | "
                f"🔧 {selected_strategies[0]:<10} | "
                f"📊 {trade_status:<15}"
            )

            print_colored(row_line, signal_color if signal != "hold" else "INFO")

            # Wait for next signal (30 seconds for demo)
            await asyncio.sleep(30)

    except KeyboardInterrupt:
        print_colored("\n🛑 Trading bot stopped by user", "WARNING")
    except Exception as e:
        print_colored(f"\n❌ Trading bot error: {str(e)}", "ERROR")
    finally:
        if quotex_client:
            quotex_client.close()

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Select account type (1-4): ").strip()

            if choice == '1':
                print_colored("⚠️  Practice mode selected", "WARNING")
                asyncio.run(run_trading_bot("PRACTICE", is_practice_only=True))
            elif choice == '2':
                print_colored("⚠️  Demo trading mode selected", "WARNING")
                asyncio.run(run_trading_bot("PRACTICE"))
            elif choice == '3':
                print_colored("🚨 Live trading mode selected", "ERROR", bold=True)
                confirm = input("⚠️  Are you sure you want to trade with real money? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    asyncio.run(run_trading_bot("REAL"))
                else:
                    print_colored("❌ Live trading cancelled", "WARNING")
            elif choice == '4':
                print_colored("👋 Goodbye! ✨ Hope Your Session Was Good", "INFO", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-4.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! ✨ Hope Your Session Was Good", "INFO", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
