#!/usr/bin/env python3
"""
Pattern Analyzer for Historical Time-Based Pattern Recognition
Analyzes candle patterns at specific times across multiple days
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from collections import defaultdict
from utils import print_colored
from config import ADVANCED_SIGNAL_CONFIG

class PatternAnalyzer:
    def __init__(self):
        """Initialize the pattern analyzer"""
        self.patterns = {}
        self.temp_files = []
    
    def find_time_patterns(self, df, analysis_days, start_time, end_time, timeframe):
        """Find patterns by analyzing every single candle within the time range across multiple days"""
        print_colored(f"🔍 Analyzing {analysis_days} days of patterns...", "INFO")

        # Parse time strings
        start_hour, start_minute = map(int, start_time.split(':'))
        end_hour, end_minute = map(int, end_time.split(':'))

        # Group data by date (handle the time format from utils.py)
        df['datetime'] = pd.to_datetime(df['time'])
        df['date'] = df['datetime'].dt.date
        df['hour'] = df['datetime'].dt.hour
        df['minute'] = df['datetime'].dt.minute
        df['weekday'] = df['datetime'].dt.weekday  # 0=Monday, 6=Sunday

        # Get only active trading days (weekdays only, exclude weekends and today)
        from datetime import datetime, date
        today = datetime.now().date()

        all_dates = sorted(df['date'].unique())
        active_trading_days = []

        # Filter out weekends, holidays, and today
        for date_val in all_dates:
            # Skip today's date to avoid incomplete data
            if date_val >= today:
                continue

            # Get weekday for this date
            date_weekday = pd.to_datetime(date_val).weekday()

            # Skip weekends (Saturday=5, Sunday=6)
            if date_weekday >= 5:
                continue

            # Check if it's a trading day by candle count (very reduced threshold)
            day_candles = len(df[df['date'] == date_val])
            if day_candles > 5:  # At least 5 candles = active trading day (extremely lenient)
                active_trading_days.append(date_val)

        # Get the last N active trading days (excluding today and weekends)
        if len(active_trading_days) >= analysis_days:
            selected_dates = active_trading_days[-analysis_days:]
        else:
            # Use whatever data we have, but pad with missing days if needed
            selected_dates = active_trading_days
            if len(selected_dates) < analysis_days:
                # Add missing days as placeholders to reach the requested count
                missing_days_needed = analysis_days - len(selected_dates)
                # Create fake dates going backwards from the earliest available date
                if selected_dates:
                    earliest_date = selected_dates[0]
                    # Convert to pandas datetime
                    earliest_pd_date = pd.to_datetime(earliest_date)

                    for i in range(1, missing_days_needed + 1):
                        fake_date = earliest_pd_date - pd.Timedelta(days=i)
                        # Only add weekdays
                        if fake_date.weekday() < 5:
                            # Convert back to date object
                            selected_dates.insert(0, fake_date.date())
                            if len(selected_dates) >= analysis_days:
                                break

        if len(selected_dates) < 1:
            print_colored(f"❌ Need at least 1 active trading day, found {len(selected_dates)}", "ERROR")
            return []



        # Collect all candles within time range for each day
        all_candles_by_time = defaultdict(list)
        total_candles_analyzed = 0

        for date in selected_dates:
            day_data = df[df['date'] == date].copy()

            # Filter by time window
            time_filtered = day_data[
                ((day_data['hour'] > start_hour) |
                 ((day_data['hour'] == start_hour) & (day_data['minute'] >= start_minute))) &
                ((day_data['hour'] < end_hour) |
                 ((day_data['hour'] == end_hour) & (day_data['minute'] <= end_minute)))
            ]

            total_candles_analyzed += len(time_filtered)

            # Analyze each candle in the time range
            for _, candle in time_filtered.iterrows():
                time_key = f"{candle['hour']:02d}:{candle['minute']:02d}"

                # Determine candle direction and strength
                candle_info = self.analyze_candle(candle)
                all_candles_by_time[time_key].append({
                    'date': date,
                    'direction': candle_info['direction'],
                    'strength': candle_info['strength'],
                    'body_ratio': candle_info['body_ratio'],
                    'price': candle['close'],
                    'time_key': time_key
                })

        # Find consistent patterns for each time slot

        consistent_patterns = self.find_consistent_patterns(all_candles_by_time, len(selected_dates), selected_dates)

        return consistent_patterns
    
    def analyze_candle(self, candle):
        """Analyze individual candle characteristics - simplified to only check direction"""
        open_price = candle['open']
        close_price = candle['close']
        high_price = candle['high']
        low_price = candle['low']

        # Calculate basic candle metrics (for strength filter only)
        body_size = abs(close_price - open_price)
        candle_range = high_price - low_price
        body_ratio = body_size / candle_range if candle_range > 0 else 0

        # Determine direction (main focus)
        if close_price > open_price:
            direction = 'BULLISH'
        elif close_price < open_price:
            direction = 'BEARISH'
        else:
            direction = 'DOJI'

        # Simple strength check (no volume analysis)
        strength_threshold = ADVANCED_SIGNAL_CONFIG['CANDLE_STRENGTH_THRESHOLD']
        strength = 'STRONG' if body_ratio >= strength_threshold else 'WEAK'

        return {
            'direction': direction,
            'strength': strength,
            'body_ratio': body_ratio
        }
    
    def find_consistent_patterns(self, time_patterns, analysis_days, selected_dates):
        """Find time slots with consistent patterns across all days"""
        consistent_patterns = []
        min_confidence = ADVANCED_SIGNAL_CONFIG['MIN_PATTERN_CONFIDENCE']

        for time_slot, candles in time_patterns.items():
            # Require at least 1 day of data to be very permissive
            if len(candles) < 1:
                continue  # Need at least 1 day of data for pattern analysis

            # Sort candles by date to ensure correct order for pattern string
            candles_sorted = sorted(candles, key=lambda x: x['date'])

            # Create a complete pattern for all selected dates (fill missing days with 'N')
            candles_by_date = {candle['date']: candle for candle in candles_sorted}
            complete_pattern_data = []

            for date in selected_dates:
                if date in candles_by_date:
                    complete_pattern_data.append(candles_by_date[date])
                else:
                    # Missing data for this date at this time slot
                    complete_pattern_data.append({
                        'date': date,
                        'direction': 'MISSING',
                        'strength': 'WEAK',
                        'body_ratio': 0.0,
                        'price': 0.0,
                        'time_key': time_slot
                    })

            # Count directions (only from actual data, not missing)
            bullish_count = sum(1 for c in candles_sorted if c['direction'] == 'BULLISH')
            bearish_count = sum(1 for c in candles_sorted if c['direction'] == 'BEARISH')
            doji_count = sum(1 for c in candles_sorted if c['direction'] == 'DOJI')

            # Count strong candles
            strong_count = sum(1 for c in candles_sorted if c['strength'] == 'STRONG')

            # Calculate consistency (based on actual data only)
            total_actual_candles = len(candles_sorted)
            if total_actual_candles > 0:
                bullish_ratio = bullish_count / total_actual_candles
                bearish_ratio = bearish_count / total_actual_candles
                strong_ratio = strong_count / total_actual_candles
            else:
                bullish_ratio = bearish_ratio = strong_ratio = 0

            # Create direction pattern string for ALL requested days (S for sell/bearish, B for buy/bullish, D for doji, N for missing)
            direction_pattern = ""
            for candle in complete_pattern_data:
                if candle['direction'] == 'BULLISH':
                    direction_pattern += 'B'
                elif candle['direction'] == 'BEARISH':
                    direction_pattern += 'S'
                elif candle['direction'] == 'DOJI':
                    direction_pattern += 'D'
                else:  # MISSING
                    direction_pattern += 'N'



            # Determine if pattern is consistent enough
            pattern_signal = None
            confidence = 0

            if bullish_ratio >= min_confidence:
                pattern_signal = 'BUY'
                confidence = bullish_ratio
            elif bearish_ratio >= min_confidence:
                pattern_signal = 'SELL'
                confidence = bearish_ratio

            if pattern_signal:
                # Calculate additional metrics (only from actual data)
                if total_actual_candles > 0:
                    avg_body_ratio = sum(c['body_ratio'] for c in candles_sorted) / total_actual_candles
                    avg_price = sum(c['price'] for c in candles_sorted) / total_actual_candles
                else:
                    avg_body_ratio = avg_price = 0

                pattern_info = {
                    'time': time_slot,
                    'signal': pattern_signal,
                    'confidence': confidence,
                    'pattern_type': f"{pattern_signal.lower()}_consistent",
                    'total_occurrences': total_actual_candles,
                    'signal_occurrences': bullish_count if pattern_signal == 'BUY' else bearish_count,
                    'strong_candles': strong_count,
                    'strong_ratio': strong_ratio,
                    'avg_body_ratio': avg_body_ratio,
                    'avg_price': avg_price,
                    'analysis_days': analysis_days,
                    'direction_pattern': direction_pattern,  # This now includes ALL requested days
                    'raw_data': complete_pattern_data  # Use complete data including missing days
                }

                consistent_patterns.append(pattern_info)

        # Sort by time instead of confidence
        consistent_patterns.sort(key=lambda x: x['time'])

        return consistent_patterns
    
    def calculate_pattern_strength(self, pattern):
        """Calculate overall pattern strength score"""
        base_score = pattern['confidence'] * 100
        
        # Bonus for strong candles
        strong_bonus = pattern['strong_ratio'] * 20
        
        # Bonus for high body ratio
        body_bonus = pattern['avg_body_ratio'] * 15
        
        # Bonus for multiple occurrences
        occurrence_bonus = min(pattern['total_occurrences'] * 2, 10)
        
        total_score = base_score + strong_bonus + body_bonus + occurrence_bonus
        
        return min(total_score, 100)  # Cap at 100
    
    def get_pattern_description(self, pattern):
        """Get human-readable pattern description"""
        signal = pattern['signal']
        time_slot = pattern['time']
        confidence = pattern['confidence'] * 100
        occurrences = pattern['signal_occurrences']
        total = pattern['total_occurrences']
        
        description = f"{signal} pattern at {time_slot}: {occurrences}/{total} days ({confidence:.1f}%)"
        
        if pattern['strong_ratio'] > 0.5:
            description += " - Strong candles"
        
        return description
    
    def export_pattern_details(self, patterns, filename=None):
        """Export detailed pattern analysis to CSV"""
        if not patterns:
            return None
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pattern_analysis_{timestamp}.csv"
        
        # Prepare data for export
        export_data = []
        for pattern in patterns:
            for candle_data in pattern['raw_data']:
                export_data.append({
                    'time_slot': pattern['time'],
                    'signal': pattern['signal'],
                    'pattern_confidence': pattern['confidence'],
                    'date': candle_data['date'],
                    'direction': candle_data['direction'],
                    'strength': candle_data['strength'],
                    'body_ratio': candle_data['body_ratio'],
                    'price': candle_data['price']
                })
        
        # Create DataFrame and save
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False)
        
        self.temp_files.append(filename)
        print_colored(f"📊 Pattern details exported to {filename}", "INFO")
        
        return filename
    
    def cleanup_temp_files(self):
        """Clean up temporary files created during analysis"""
        import os
        
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print_colored(f"🗑️ Removed {file_path}", "INFO")
            except Exception as e:
                print_colored(f"⚠️ Could not remove {file_path}: {str(e)}", "WARNING")
        
        self.temp_files.clear()
    
    def get_next_signal_time(self, patterns, current_time=None):
        """Get the next upcoming signal time based on patterns"""
        if not patterns:
            return None
        
        if current_time is None:
            current_time = datetime.now().time()
        
        # Convert pattern times to time objects and sort
        pattern_times = []
        for pattern in patterns:
            hour, minute = map(int, pattern['time'].split(':'))
            pattern_time = time(hour, minute)
            pattern_times.append((pattern_time, pattern))
        
        pattern_times.sort(key=lambda x: x[0])
        
        # Find next signal time
        for pattern_time, pattern in pattern_times:
            if pattern_time > current_time:
                return {
                    'time': pattern['time'],
                    'signal': pattern['signal'],
                    'confidence': pattern['confidence'],
                    'minutes_until': self.calculate_minutes_until(current_time, pattern_time)
                }
        
        # If no time today, return first time tomorrow
        if pattern_times:
            first_pattern = pattern_times[0][1]
            return {
                'time': first_pattern['time'],
                'signal': first_pattern['signal'],
                'confidence': first_pattern['confidence'],
                'minutes_until': self.calculate_minutes_until(current_time, pattern_times[0][0]) + 1440  # +24 hours
            }
        
        return None
    
    def calculate_minutes_until(self, current_time, target_time):
        """Calculate minutes until target time"""
        current_minutes = current_time.hour * 60 + current_time.minute
        target_minutes = target_time.hour * 60 + target_time.minute
        
        if target_minutes > current_minutes:
            return target_minutes - current_minutes
        else:
            return (24 * 60) - current_minutes + target_minutes  # Next day
