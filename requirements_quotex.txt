# Quotex Trading Bot Requirements
# Install with: pip install -r requirements_quotex.txt

# Core dependencies
pyquotex>=1.0.0
python-dotenv>=0.19.0
asyncio-mqtt>=0.11.0

# Data analysis and processing
pandas>=1.3.0
numpy>=1.21.0

# HTTP requests and API handling
requests>=2.25.0
aiohttp>=3.8.0

# Async support
asyncio>=3.4.3

# Optional: For advanced technical analysis
ta-lib>=0.4.0  # Technical Analysis Library (optional)
plotly>=5.0.0  # For plotting (optional)

# Development and testing (optional)
pytest>=6.0.0
black>=21.0.0
flake8>=3.9.0
