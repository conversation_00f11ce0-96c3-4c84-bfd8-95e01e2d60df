#!/usr/bin/env python3
"""
Advanced Signal Generator with Historical Pattern Recognition
Analyzes historical patterns at specific times and generates advanced trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import time as time_module
from pattern_analyzer import PatternAnalyzer
from advanced_filters import AdvancedFilters
from utils import (
    fetch_historical_candles, print_colored, print_header, 
    select_currency_pairs, format_price, format_percentage
)
from config import CURRENCY_PAIRS, ADVANCED_SIGNAL_CONFIG, DISPLAY_CONFIG

class AdvancedSignalGenerator:
    def __init__(self):
        """Initialize the advanced signal generator"""
        self.pattern_analyzer = PatternAnalyzer()
        self.advanced_filters = AdvancedFilters()
        self.signals = []
        
    def get_user_inputs(self):
        """Get user inputs for signal generation parameters"""
        print_header("🔧 SIGNAL GENERATION SETUP")
        
        # Select currency pairs
        print_colored("📊 Select Currency Pairs:", "INFO", bold=True)
        selected_pairs = select_currency_pairs()
        print()
        
        # Select timeframe
        print_colored("⏰ Select Timeframe:", "INFO", bold=True)
        timeframes = ADVANCED_SIGNAL_CONFIG["TIMEFRAMES"]
        timeframe_names = ADVANCED_SIGNAL_CONFIG["TIMEFRAME_NAMES"]
        
        for i, tf in enumerate(timeframes, 1):
            print_colored(f"{i}. {timeframe_names[tf]} ({tf})", "INFO")
        
        while True:
            try:
                tf_choice = int(input("\nEnter timeframe choice (1-5): ").strip())
                if 1 <= tf_choice <= len(timeframes):
                    selected_timeframe = timeframes[tf_choice - 1]
                    break
                else:
                    print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid number.", "ERROR")
        
        print_colored(f"✅ Selected: {timeframe_names[selected_timeframe]}", "SUCCESS")
        print()
        
        # Number of days to analyze
        print_colored("📅 Analysis Period:", "INFO", bold=True)
        default_days = ADVANCED_SIGNAL_CONFIG["DEFAULT_ANALYSIS_DAYS"]
        min_days = ADVANCED_SIGNAL_CONFIG["MIN_ANALYSIS_DAYS"]
        max_days = ADVANCED_SIGNAL_CONFIG["MAX_ANALYSIS_DAYS"]
        
        print_colored(f"Enter number of days to analyze ({min_days}-{max_days}, default: {default_days}):", "INFO")
        
        while True:
            try:
                days_input = input("Days: ").strip()
                if not days_input:
                    analysis_days = default_days
                    break
                
                analysis_days = int(days_input)
                if min_days <= analysis_days <= max_days:
                    break
                else:
                    print_colored(f"❌ Please enter a number between {min_days} and {max_days}.", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid number.", "ERROR")
        
        print_colored(f"✅ Analyzing {analysis_days} days of historical data", "SUCCESS")
        print()
        
        # Time window
        print_colored("🕐 Analysis Time Window:", "INFO", bold=True)
        default_start = ADVANCED_SIGNAL_CONFIG["DEFAULT_START_TIME"]
        default_end = ADVANCED_SIGNAL_CONFIG["DEFAULT_END_TIME"]
        
        print_colored(f"Enter start time (HH:MM format, default: {default_start}):", "INFO")
        start_time_input = input("Start time: ").strip()
        start_time = start_time_input if start_time_input else default_start
        
        print_colored(f"Enter end time (HH:MM format, default: {default_end}):", "INFO")
        end_time_input = input("End time: ").strip()
        end_time = end_time_input if end_time_input else default_end
        
        print_colored(f"✅ Time window: {start_time} - {end_time}", "SUCCESS")
        print()

        # Get confidence threshold
        print_colored("🎯 Confidence Threshold:", "INFO", bold=True)
        print_colored("Set minimum confidence level to filter signals", "INFO")
        print_colored("Higher values = fewer but more reliable signals", "INFO")

        confidence_input = input("Enter minimum confidence % (30-95, default: 50):\nConfidence: ").strip()
        if not confidence_input:
            min_confidence = 50
        else:
            try:
                min_confidence = int(confidence_input)
                if min_confidence < 30:
                    min_confidence = 30
                elif min_confidence > 95:
                    min_confidence = 95
            except ValueError:
                min_confidence = 50

        print_colored(f"✅ Minimum confidence: {min_confidence}%", "SUCCESS")
        print()

        return {
            'pairs': selected_pairs,
            'timeframe': selected_timeframe,
            'analysis_days': analysis_days,
            'start_time': start_time,
            'end_time': end_time,
            'min_confidence': min_confidence
        }
    
    def generate_signals(self, params):
        """Generate advanced signals based on historical pattern analysis"""
        print_header("🔍 ANALYZING HISTORICAL PATTERNS")
        print_colored("📊 Using historical data to predict TODAY's signals", "INFO", bold=True)
        print_colored("⚠️ Note: Today's incomplete candles are excluded from analysis", "WARNING")
        print()

        all_signals = []
        
        for pair in params['pairs']:
            print_colored(f"📊 Analyzing {pair}...", "INFO")
            
            try:
                # Fetch historical data with much larger buffer to ensure enough trading days
                # Use a much larger buffer, especially for M1 timeframe
                if params['timeframe'] == 'M1':
                    # M1 needs much more data due to higher granularity
                    buffer_multiplier = max(4.0, params['analysis_days'] * 1.0)  # 4x buffer minimum
                    fetch_days = int(params['analysis_days'] + buffer_multiplier + 15)  # Extra 15 days for M1
                else:
                    # Other timeframes use smaller buffer
                    buffer_multiplier = max(2.5, params['analysis_days'] * 0.5)  # At least 2.5x buffer
                    fetch_days = int(params['analysis_days'] + buffer_multiplier + 8)  # Extra 8 days safety

                historical_data = self.fetch_extended_historical_data(
                    pair, params['timeframe'], fetch_days
                )
                
                if historical_data is None or len(historical_data) < 100:
                    print_colored(f"❌ Insufficient data for {pair}", "ERROR")
                    continue
                
                # Analyze patterns
                patterns = self.pattern_analyzer.find_time_patterns(
                    historical_data, 
                    params['analysis_days'],
                    params['start_time'],
                    params['end_time'],
                    params['timeframe']
                )
                
                # Apply advanced filters
                filtered_signals = self.advanced_filters.filter_signals(
                    patterns, historical_data, pair
                )
                
                # Add to results with today's date and day information
                for signal in filtered_signals:
                    signal['pair'] = pair
                    signal['timeframe'] = params['timeframe']

                    # Add today's date and day information
                    today = datetime.now()
                    signal['target_date'] = today.strftime('%Y-%m-%d')
                    signal['target_day'] = today.strftime('%A')
                    signal['target_time'] = signal['time']

                    all_signals.append(signal)
                    
            except Exception as e:
                print_colored(f"❌ Error analyzing {pair}: {str(e)}", "ERROR")
                continue
        
        # Filter signals to only show future times for today
        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute

        future_signals = []
        for signal in all_signals:
            signal_hour, signal_minute = map(int, signal['time'].split(':'))

            # Only include signals for times that haven't passed yet today
            if signal_hour > current_hour or (signal_hour == current_hour and signal_minute > current_minute):
                future_signals.append(signal)

        if len(future_signals) < len(all_signals):
            print_colored(f"🕐 Filtered to {len(future_signals)} future signals (current time: {current_time.strftime('%H:%M')})", "INFO")

        # Apply confidence threshold filtering
        confidence_filtered_signals = []
        for signal in future_signals:
            signal_confidence = signal.get('confidence_score', 0)
            if signal_confidence >= params['min_confidence']:
                confidence_filtered_signals.append(signal)

        if len(confidence_filtered_signals) < len(future_signals):
            filtered_count = len(future_signals) - len(confidence_filtered_signals)
            print_colored(f"🎯 Filtered out {filtered_count} signals below {params['min_confidence']}% confidence", "INFO")

        return confidence_filtered_signals
    
    def fetch_extended_historical_data(self, pair, timeframe, days):
        """Fetch extended historical data for pattern analysis"""
        try:
            # Calculate the number of candles needed
            candles_per_day = {
                'M1': 1440,   # 24 * 60
                'M5': 288,    # 24 * 12
                'M15': 96,    # 24 * 4
                'M30': 48,    # 24 * 2
                'H1': 24      # 24 * 1
            }

            # Add extra buffer to ensure we have enough data after excluding today
            # Use a much larger buffer to guarantee sufficient trading days
            if timeframe == 'M1':
                buffer_days = days + 15  # Much larger buffer for M1 timeframe
            else:
                buffer_days = days + 10  # Larger buffer for other timeframes
            total_candles = candles_per_day.get(timeframe, 1440) * buffer_days

            print_colored(f"📊 Requesting {total_candles} candles ({buffer_days} days buffer) for {pair}", "INFO")

            # Fetch historical data
            return fetch_historical_candles(pair, total_candles, timeframe)
            
        except Exception as e:
            print_colored(f"❌ Error fetching data for {pair}: {str(e)}", "ERROR")
            return None
    
    def display_signals(self, signals, min_confidence=None):
        """Display generated signals in a formatted table"""
        if not signals:
            print_colored("❌ No signals found matching the criteria", "WARNING")
            if min_confidence:
                print_colored(f"💡 Try lowering the confidence threshold (currently {min_confidence}%)", "INFO")
            return

        print_header(f"🎯 ADVANCED SIGNALS GENERATED ({len(signals)} signals)")

        if min_confidence:
            print_colored(f"🎯 Showing signals with ≥{min_confidence}% confidence", "INFO")
            print()

        # Sort signals by time (earliest first)
        signals.sort(key=lambda x: x.get('time', '00:00'))

        # Print table header
        self.print_signal_table_header()

        # Print signals
        for signal in signals:
            self.print_signal_row(signal)

        print()
        print_colored(f"📊 Total signals: {len(signals)}", "INFO", bold=True)

        # Summary statistics
        buy_signals = [s for s in signals if s['signal'] == 'BUY']
        sell_signals = [s for s in signals if s['signal'] == 'SELL']

        print_colored(f"📈 BUY signals: {len(buy_signals)}", "BUY")
        print_colored(f"📉 SELL signals: {len(sell_signals)}", "SELL")

        if signals:
            avg_confidence = sum(s.get('confidence_score', 0) for s in signals) / len(signals)
            print_colored(f"🎯 Average confidence: {avg_confidence:.1f}%", "SUCCESS")

            # Show confidence distribution
            high_conf = len([s for s in signals if s.get('confidence_score', 0) >= 80])
            med_conf = len([s for s in signals if 60 <= s.get('confidence_score', 0) < 80])
            low_conf = len([s for s in signals if s.get('confidence_score', 0) < 60])

            print_colored(f"📈 High confidence (≥80%): {high_conf}", "SUCCESS")
            print_colored(f"📊 Medium confidence (60-79%): {med_conf}", "WARNING")
            print_colored(f"📉 Lower confidence (<60%): {low_conf}", "ERROR")
    
    def print_signal_table_header(self):
        """Print the signal table header with enhanced information"""
        headers = ["Date", "Day", "Time", "Pair", "Signal", "Pattern", "Confidence", "Score"]
        widths = [12, 10, 8, 10, 8, 10, 12, 8]
        colors = ["HEADER"] * len(headers)

        # Print separator
        print_colored("=" * 100, "HEADER")

        # Print headers
        row = ""
        for header, width, color in zip(headers, widths, colors):
            color_code = DISPLAY_CONFIG["COLORS"][color]
            reset_code = DISPLAY_CONFIG["COLORS"]["RESET"]
            row += f"{color_code}{header:<{width}}{reset_code}"
        print(row)

        print_colored("=" * 100, "HEADER")
    
    def print_signal_row(self, signal):
        """Print a single signal row with enhanced information"""
        target_date = signal.get('target_date', 'N/A')
        target_day = signal.get('target_day', 'N/A')[:3]  # Abbreviate day name
        time_str = signal.get('time', 'N/A')
        pair = signal.get('pair', 'N/A')
        signal_type = signal.get('signal', 'N/A')
        pattern = signal.get('direction_pattern', 'N/A')  # Get the direction pattern
        confidence = f"{signal.get('confidence_score', 0):.1f}%"
        score = f"{signal.get('total_score', 0):.0f}"

        # Determine colors
        signal_color = "BUY" if signal_type == "BUY" else "SELL" if signal_type == "SELL" else "INFO"
        confidence_color = "SUCCESS" if signal.get('confidence_score', 0) >= 80 else "WARNING" if signal.get('confidence_score', 0) >= 60 else "ERROR"

        values = [target_date, target_day, time_str, pair, signal_type, pattern, confidence, score]
        widths = [12, 10, 8, 10, 8, 10, 12, 8]
        colors = ["INFO", "INFO", "INFO", "INFO", signal_color, "INFO", confidence_color, "SUCCESS"]

        row = ""
        for value, width, color in zip(values, widths, colors):
            color_code = DISPLAY_CONFIG["COLORS"][color]
            reset_code = DISPLAY_CONFIG["COLORS"]["RESET"]
            row += f"{color_code}{str(value):<{width}}{reset_code}"
        print(row)

def main():
    """Main function for advanced signal generator"""
    try:
        generator = AdvancedSignalGenerator()
        
        # Get user inputs
        params = generator.get_user_inputs()
        
        # Generate signals
        print_colored("🚀 Starting advanced signal generation...", "INFO", bold=True)
        print()
        
        signals = generator.generate_signals(params)

        # Display results
        generator.display_signals(signals, params['min_confidence'])
        
        # Clean up temporary files
        print()
        print_colored("🧹 Cleaning up temporary files...", "INFO")
        # Cleanup logic will be implemented in pattern_analyzer
        
        print_colored("✅ Advanced signal generation completed!", "SUCCESS", bold=True)
        
    except KeyboardInterrupt:
        print_colored("\n⚠️ Signal generation interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in signal generation: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
